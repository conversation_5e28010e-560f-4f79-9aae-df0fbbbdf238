<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// تضمين ملف قاعدة البيانات
require_once 'config/database.php';

// الحصول على طريقة الطلب
$method = $_SERVER['REQUEST_METHOD'];

// التعامل مع طلبات OPTIONS (CORS)
if ($method == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// الحصول على البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

// تحديد العملية المطلوبة
$action = isset($_GET['action']) ? $_GET['action'] : (isset($input['action']) ? $input['action'] : '');

switch ($action) {
    case 'login':
        login($input);
        break;
    case 'register':
        register($input);
        break;
    case 'logout':
        logout($input);
        break;
    case 'verify_token':
        verifyToken($input);
        break;
    case 'get_user_info':
        getUserInfo($input);
        break;
    default:
        sendResponse(400, false, 'عملية غير صحيحة - Invalid action');
        break;
}

// دالة تسجيل الدخول
function login($data) {
    $con = dbConnect();
    
    // التحقق من البيانات المطلوبة
    if (!isset($data['username']) || !isset($data['password'])) {
        sendResponse(400, false, 'اسم المستخدم وكلمة المرور مطلوبان - Username and password are required');
        return;
    }
    
    $username = mysqli_real_escape_string($con, $data['username']);
    $password = $data['password'];
    
    // البحث عن المستخدم
    $query = "SELECT * FROM users WHERE (username = '$username' OR email = '$username') AND is_active = 1";
    $result = mysqli_query($con, $query);
    
    if (mysqli_num_rows($result) == 1) {
        $user = mysqli_fetch_assoc($result);
        
        // التحقق من كلمة المرور
        if (password_verify($password, $user['password'])) {
            // إنشاء رمز الجلسة
            $token = generateToken();
            $expires_at = date('Y-m-d H:i:s', strtotime('+24 hours'));
            
            // حفظ الجلسة في قاعدة البيانات
            $session_query = "INSERT INTO user_sessions (user_id, session_token, expires_at) VALUES ('{$user['id']}', '$token', '$expires_at')";
            
            if (mysqli_query($con, $session_query)) {
                // تحديث آخر تسجيل دخول
                $update_query = "UPDATE users SET last_login = NOW() WHERE id = '{$user['id']}'";
                mysqli_query($con, $update_query);
                
                // إرسال الاستجابة
                sendResponse(200, true, 'تم تسجيل الدخول بنجاح - Login successful', [
                    'token' => $token,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email'],
                        'full_name' => $user['full_name'],
                        'role' => $user['role']
                    ],
                    'expires_at' => $expires_at
                ]);
            } else {
                sendResponse(500, false, 'خطأ في إنشاء الجلسة - Session creation error');
            }
        } else {
            sendResponse(401, false, 'كلمة المرور غير صحيحة - Invalid password');
        }
    } else {
        sendResponse(401, false, 'اسم المستخدم غير موجود - User not found');
    }
    
    mysqli_close($con);
}

// دالة تسجيل مستخدم جديد
function register($data) {
    $con = dbConnect();
    
    // التحقق من البيانات المطلوبة
    $required_fields = ['username', 'email', 'password', 'full_name'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            sendResponse(400, false, "الحقل $field مطلوب - Field $field is required");
            return;
        }
    }
    
    $username = mysqli_real_escape_string($con, $data['username']);
    $email = mysqli_real_escape_string($con, $data['email']);
    $password = password_hash($data['password'], PASSWORD_DEFAULT);
    $full_name = mysqli_real_escape_string($con, $data['full_name']);
    $role = isset($data['role']) ? mysqli_real_escape_string($con, $data['role']) : 'employee';
    
    // التحقق من عدم وجود المستخدم مسبقاً
    $check_query = "SELECT id FROM users WHERE username = '$username' OR email = '$email'";
    $check_result = mysqli_query($con, $check_query);
    
    if (mysqli_num_rows($check_result) > 0) {
        sendResponse(409, false, 'اسم المستخدم أو البريد الإلكتروني موجود مسبقاً - Username or email already exists');
        return;
    }
    
    // إدراج المستخدم الجديد
    $insert_query = "INSERT INTO users (username, email, password, full_name, role) VALUES ('$username', '$email', '$password', '$full_name', '$role')";
    
    if (mysqli_query($con, $insert_query)) {
        $user_id = mysqli_insert_id($con);
        
        sendResponse(201, true, 'تم إنشاء الحساب بنجاح - Account created successfully', [
            'user_id' => $user_id,
            'username' => $username,
            'email' => $email,
            'full_name' => $full_name,
            'role' => $role
        ]);
    } else {
        sendResponse(500, false, 'خطأ في إنشاء الحساب - Account creation error: ' . mysqli_error($con));
    }
    
    mysqli_close($con);
}

// دالة تسجيل الخروج
function logout($data) {
    $con = dbConnect();
    
    if (!isset($data['token'])) {
        sendResponse(400, false, 'رمز الجلسة مطلوب - Token is required');
        return;
    }
    
    $token = mysqli_real_escape_string($con, $data['token']);
    
    // حذف الجلسة
    $delete_query = "DELETE FROM user_sessions WHERE session_token = '$token'";
    
    if (mysqli_query($con, $delete_query)) {
        sendResponse(200, true, 'تم تسجيل الخروج بنجاح - Logout successful');
    } else {
        sendResponse(500, false, 'خطأ في تسجيل الخروج - Logout error');
    }
    
    mysqli_close($con);
}

// دالة التحقق من صحة الرمز المميز
function verifyToken($data) {
    $con = dbConnect();
    
    if (!isset($data['token'])) {
        sendResponse(400, false, 'رمز الجلسة مطلوب - Token is required');
        return;
    }
    
    $token = mysqli_real_escape_string($con, $data['token']);
    
    // البحث عن الجلسة
    $query = "SELECT s.*, u.username, u.email, u.full_name, u.role 
              FROM user_sessions s 
              JOIN users u ON s.user_id = u.id 
              WHERE s.session_token = '$token' AND s.expires_at > NOW() AND u.is_active = 1";
    
    $result = mysqli_query($con, $query);
    
    if (mysqli_num_rows($result) == 1) {
        $session = mysqli_fetch_assoc($result);
        
        sendResponse(200, true, 'الرمز صحيح - Token is valid', [
            'user' => [
                'id' => $session['user_id'],
                'username' => $session['username'],
                'email' => $session['email'],
                'full_name' => $session['full_name'],
                'role' => $session['role']
            ],
            'expires_at' => $session['expires_at']
        ]);
    } else {
        sendResponse(401, false, 'رمز غير صحيح أو منتهي الصلاحية - Invalid or expired token');
    }
    
    mysqli_close($con);
}

// دالة الحصول على معلومات المستخدم
function getUserInfo($data) {
    $con = dbConnect();
    
    if (!isset($data['token'])) {
        sendResponse(400, false, 'رمز الجلسة مطلوب - Token is required');
        return;
    }
    
    $token = mysqli_real_escape_string($con, $data['token']);
    
    // البحث عن المستخدم من خلال الرمز
    $query = "SELECT u.id, u.username, u.email, u.full_name, u.role, u.last_login, u.created_at
              FROM user_sessions s 
              JOIN users u ON s.user_id = u.id 
              WHERE s.session_token = '$token' AND s.expires_at > NOW() AND u.is_active = 1";
    
    $result = mysqli_query($con, $query);
    
    if (mysqli_num_rows($result) == 1) {
        $user = mysqli_fetch_assoc($result);
        
        sendResponse(200, true, 'تم الحصول على معلومات المستخدم - User info retrieved', [
            'user' => $user
        ]);
    } else {
        sendResponse(401, false, 'رمز غير صحيح أو منتهي الصلاحية - Invalid or expired token');
    }
    
    mysqli_close($con);
}

// دالة إنشاء رمز عشوائي
function generateToken() {
    return bin2hex(random_bytes(32));
}

// دالة إرسال الاستجابة
function sendResponse($status_code, $success, $message, $data = null) {
    http_response_code($status_code);
    
    $response = [
        'success' => $success,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit();
}

?>
