<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API تسجيل الدخول - Auth API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .response {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 اختبار API تسجيل الدخول - Auth API Test</h1>
        
        <!-- قسم تسجيل الدخول -->
        <div class="section">
            <h2>تسجيل الدخول - Login</h2>
            <div class="form-group">
                <label>اسم المستخدم أو البريد الإلكتروني:</label>
                <input type="text" id="loginUsername" placeholder="admin أو <EMAIL>">
            </div>
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" id="loginPassword" placeholder="123456">
            </div>
            <button onclick="login()">تسجيل الدخول</button>
        </div>

        <!-- قسم تسجيل مستخدم جديد -->
        <div class="section">
            <h2>تسجيل مستخدم جديد - Register</h2>
            <div class="form-group">
                <label>اسم المستخدم:</label>
                <input type="text" id="regUsername" placeholder="newuser">
            </div>
            <div class="form-group">
                <label>البريد الإلكتروني:</label>
                <input type="email" id="regEmail" placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" id="regPassword" placeholder="password123">
            </div>
            <div class="form-group">
                <label>الاسم الكامل:</label>
                <input type="text" id="regFullName" placeholder="الاسم الكامل">
            </div>
            <div class="form-group">
                <label>الدور:</label>
                <select id="regRole">
                    <option value="employee">موظف - Employee</option>
                    <option value="manager">مدير - Manager</option>
                    <option value="admin">مدير عام - Admin</option>
                </select>
            </div>
            <button onclick="register()">تسجيل مستخدم جديد</button>
        </div>

        <!-- قسم التحقق من الرمز -->
        <div class="section">
            <h2>التحقق من الرمز - Verify Token</h2>
            <div class="form-group">
                <label>رمز الجلسة:</label>
                <input type="text" id="verifyToken" placeholder="أدخل الرمز هنا">
            </div>
            <button onclick="verifyToken()">التحقق من الرمز</button>
            <button onclick="getUserInfo()">معلومات المستخدم</button>
        </div>

        <!-- قسم تسجيل الخروج -->
        <div class="section">
            <h2>تسجيل الخروج - Logout</h2>
            <div class="form-group">
                <label>رمز الجلسة:</label>
                <input type="text" id="logoutToken" placeholder="أدخل الرمز هنا">
            </div>
            <button onclick="logout()">تسجيل الخروج</button>
        </div>

        <!-- عرض النتائج -->
        <div id="response"></div>
    </div>

    <script>
        const API_URL = 'auth.php';
        let currentToken = '';

        // دالة تسجيل الدخول
        async function login() {
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            if (!username || !password) {
                showResponse('يرجى إدخال اسم المستخدم وكلمة المرور', false);
                return;
            }

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'login',
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();
                showResponse(JSON.stringify(data, null, 2), data.success);

                if (data.success && data.data.token) {
                    currentToken = data.data.token;
                    document.getElementById('verifyToken').value = currentToken;
                    document.getElementById('logoutToken').value = currentToken;
                }
            } catch (error) {
                showResponse('خطأ في الاتصال: ' + error.message, false);
            }
        }

        // دالة تسجيل مستخدم جديد
        async function register() {
            const username = document.getElementById('regUsername').value;
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            const fullName = document.getElementById('regFullName').value;
            const role = document.getElementById('regRole').value;

            if (!username || !email || !password || !fullName) {
                showResponse('يرجى ملء جميع الحقول المطلوبة', false);
                return;
            }

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'register',
                        username: username,
                        email: email,
                        password: password,
                        full_name: fullName,
                        role: role
                    })
                });

                const data = await response.json();
                showResponse(JSON.stringify(data, null, 2), data.success);
            } catch (error) {
                showResponse('خطأ في الاتصال: ' + error.message, false);
            }
        }

        // دالة التحقق من الرمز
        async function verifyToken() {
            const token = document.getElementById('verifyToken').value;

            if (!token) {
                showResponse('يرجى إدخال رمز الجلسة', false);
                return;
            }

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'verify_token',
                        token: token
                    })
                });

                const data = await response.json();
                showResponse(JSON.stringify(data, null, 2), data.success);
            } catch (error) {
                showResponse('خطأ في الاتصال: ' + error.message, false);
            }
        }

        // دالة الحصول على معلومات المستخدم
        async function getUserInfo() {
            const token = document.getElementById('verifyToken').value;

            if (!token) {
                showResponse('يرجى إدخال رمز الجلسة', false);
                return;
            }

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_user_info',
                        token: token
                    })
                });

                const data = await response.json();
                showResponse(JSON.stringify(data, null, 2), data.success);
            } catch (error) {
                showResponse('خطأ في الاتصال: ' + error.message, false);
            }
        }

        // دالة تسجيل الخروج
        async function logout() {
            const token = document.getElementById('logoutToken').value;

            if (!token) {
                showResponse('يرجى إدخال رمز الجلسة', false);
                return;
            }

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'logout',
                        token: token
                    })
                });

                const data = await response.json();
                showResponse(JSON.stringify(data, null, 2), data.success);

                if (data.success) {
                    currentToken = '';
                    document.getElementById('verifyToken').value = '';
                    document.getElementById('logoutToken').value = '';
                }
            } catch (error) {
                showResponse('خطأ في الاتصال: ' + error.message, false);
            }
        }

        // دالة عرض النتائج
        function showResponse(message, isSuccess) {
            const responseDiv = document.getElementById('response');
            responseDiv.innerHTML = message;
            responseDiv.className = 'response ' + (isSuccess ? 'success' : 'error');
        }
    </script>
</body>
</html>
