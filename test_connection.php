<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات
 * Database Connection Test File
 */

// تضمين ملف قاعدة البيانات
require_once 'config/database.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال بقاعدة البيانات - Database Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .success {
            color: green;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            color: red;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .config-info {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 اختبار الاتصال بقاعدة البيانات</h1>
        <h1>Database Connection Test</h1>
        
        <div class="config-info">
            <h3>إعدادات قاعدة البيانات الحالية - Current Database Settings:</h3>
            <ul>
                <li><strong>المضيف - Host:</strong> <?php echo DB_HOST; ?></li>
                <li><strong>اسم قاعدة البيانات - Database Name:</strong> <?php echo DB_NAME; ?></li>
                <li><strong>اسم المستخدم - Username:</strong> <?php echo DB_USER; ?></li>
                <li><strong>ترميز الأحرف - Charset:</strong> <?php echo DB_CHARSET; ?></li>
            </ul>
        </div>

        <?php
        // اختبار الاتصال
        echo "<h3>🧪 نتيجة اختبار الاتصال - Connection Test Result:</h3>";
        
        try {
            // إنشاء مثيل جديد من قاعدة البيانات
            $db = new Database();
            $connection = $db->getConnection();
            
            if ($connection) {
                echo '<div class="success">';
                echo '<h4>✅ تم الاتصال بنجاح! - Connection Successful!</h4>';
                echo '<p>تم إنشاء الاتصال مع قاعدة البيانات بنجاح.</p>';
                echo '<p>Database connection established successfully.</p>';
                echo '</div>';
                
                // اختبار إضافي - الحصول على معلومات الخادم
                try {
                    $serverInfo = $connection->getAttribute(PDO::ATTR_SERVER_INFO);
                    $serverVersion = $connection->getAttribute(PDO::ATTR_SERVER_VERSION);
                    
                    echo '<div class="info">';
                    echo '<h4>📊 معلومات الخادم - Server Information:</h4>';
                    echo '<p><strong>إصدار الخادم - Server Version:</strong> ' . $serverVersion . '</p>';
                    echo '<p><strong>معلومات الخادم - Server Info:</strong> ' . $serverInfo . '</p>';
                    echo '</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="error">';
                    echo '<p>تعذر الحصول على معلومات الخادم: ' . $e->getMessage() . '</p>';
                    echo '<p>Could not retrieve server information: ' . $e->getMessage() . '</p>';
                    echo '</div>';
                }
                
            } else {
                echo '<div class="error">';
                echo '<h4>❌ فشل الاتصال! - Connection Failed!</h4>';
                echo '<p>لم يتم إنشاء الاتصال مع قاعدة البيانات.</p>';
                echo '<p>Database connection could not be established.</p>';
                echo '</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="error">';
            echo '<h4>❌ خطأ في الاتصال! - Connection Error!</h4>';
            echo '<p><strong>رسالة الخطأ - Error Message:</strong> ' . $e->getMessage() . '</p>';
            echo '</div>';
        }
        ?>
        
        <div class="info">
            <h3>📝 ملاحظات مهمة - Important Notes:</h3>
            <ul>
                <li>تأكد من تشغيل خادم MySQL أو MariaDB</li>
                <li>Make sure MySQL or MariaDB server is running</li>
                <li>تأكد من وجود قاعدة البيانات "warehouse_db"</li>
                <li>Make sure the database "warehouse_db" exists</li>
                <li>تحقق من صحة بيانات الاتصال في ملف config/database.php</li>
                <li>Verify connection credentials in config/database.php</li>
            </ul>
        </div>
        
        <div class="info">
            <h3>🛠️ إنشاء قاعدة البيانات - Create Database:</h3>
            <p>إذا لم تكن قاعدة البيانات موجودة، يمكنك إنشاؤها باستخدام الأمر التالي:</p>
            <p>If the database doesn't exist, you can create it using:</p>
            <code style="background: #f8f9fa; padding: 5px; border-radius: 3px;">
                CREATE DATABASE warehouse_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
            </code>
        </div>
    </div>
</body>
</html>
