-- إنشاء قاعدة بيانات المستودع
-- Create Warehouse Database

CREATE DATABASE IF NOT EXISTS warehouse_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE warehouse_db;

-- جدول الفئات - Categories Table
CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الموردين - Suppliers Table
CREATE TABLE IF NOT EXISTS suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المنتجات - Products Table
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    sku VARCHAR(50) UNIQUE NOT NULL,
    category_id INT,
    supplier_id INT,
    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    quantity_in_stock INT NOT NULL DEFAULT 0,
    minimum_stock_level INT DEFAULT 10,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL
);

-- جدول المستخدمين - Users Table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'manager', 'employee') DEFAULT 'employee',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول حركات المخزون - Stock Movements Table
CREATE TABLE IF NOT EXISTS stock_movements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    movement_type ENUM('in', 'out', 'adjustment') NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2),
    total_value DECIMAL(10,2),
    reference_number VARCHAR(50),
    notes TEXT,
    user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول أوامر الشراء - Purchase Orders Table
CREATE TABLE IF NOT EXISTS purchase_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id INT NOT NULL,
    order_date DATE NOT NULL,
    expected_delivery_date DATE,
    status ENUM('pending', 'confirmed', 'delivered', 'cancelled') DEFAULT 'pending',
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    notes TEXT,
    user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول تفاصيل أوامر الشراء - Purchase Order Details Table
CREATE TABLE IF NOT EXISTS purchase_order_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    purchase_order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    received_quantity INT DEFAULT 0,
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- إدراج بيانات تجريبية - Insert Sample Data

-- إدراج فئات تجريبية
INSERT INTO categories (name, description) VALUES
('إلكترونيات', 'أجهزة إلكترونية ومكوناتها'),
('مكتبية', 'لوازم مكتبية وقرطاسية'),
('تنظيف', 'مواد ومعدات التنظيف'),
('أثاث', 'أثاث مكتبي ومنزلي');

-- إدراج موردين تجريبيين
INSERT INTO suppliers (name, contact_person, phone, email, address) VALUES
('شركة التقنية المتقدمة', 'أحمد محمد', '01234567890', '<EMAIL>', 'الرياض، المملكة العربية السعودية'),
('مؤسسة اللوازم المكتبية', 'فاطمة علي', '01987654321', '<EMAIL>', 'جدة، المملكة العربية السعودية'),
('شركة النظافة الشاملة', 'محمد حسن', '01122334455', '<EMAIL>', 'الدمام، المملكة العربية السعودية');

-- إدراج منتجات تجريبية
INSERT INTO products (name, description, sku, category_id, supplier_id, unit_price, quantity_in_stock, minimum_stock_level) VALUES
('لابتوب ديل', 'لابتوب ديل انسبايرون 15', 'DELL-INS-15', 1, 1, 2500.00, 10, 5),
('طابعة HP', 'طابعة HP LaserJet Pro', 'HP-LJ-PRO', 1, 1, 800.00, 5, 2),
('ورق A4', 'ورق طباعة A4 - 500 ورقة', 'PAPER-A4-500', 2, 2, 25.00, 100, 20),
('أقلام جاف', 'أقلام جاف زرقاء - عبوة 12 قلم', 'PEN-BLUE-12', 2, 2, 15.00, 50, 10),
('منظف أرضيات', 'منظف أرضيات متعدد الاستخدامات', 'FLOOR-CLEAN-1L', 3, 3, 12.00, 30, 10),
('كرسي مكتب', 'كرسي مكتب دوار مريح', 'OFFICE-CHAIR-01', 4, 2, 450.00, 8, 3);

-- إدراج مستخدم إداري تجريبي
-- كلمة المرور: admin123 (مشفرة باستخدام password_hash)
INSERT INTO users (username, email, password, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin'),
('manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير المستودع', 'manager'),
('employee', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'موظف المستودع', 'employee');

-- إدراج حركات مخزون تجريبية
INSERT INTO stock_movements (product_id, movement_type, quantity, unit_price, total_value, reference_number, notes, user_id) VALUES
(1, 'in', 10, 2500.00, 25000.00, 'PO-001', 'استلام دفعة جديدة من اللابتوبات', 1),
(2, 'in', 5, 800.00, 4000.00, 'PO-002', 'استلام طابعات جديدة', 1),
(3, 'in', 100, 25.00, 2500.00, 'PO-003', 'تجديد مخزون الورق', 2),
(1, 'out', 2, 2500.00, 5000.00, 'SO-001', 'بيع لابتوبين للعميل', 2);

-- إنشاء فهارس لتحسين الأداء - Create Indexes for Performance
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_supplier ON products(supplier_id);
CREATE INDEX idx_stock_movements_product ON stock_movements(product_id);
CREATE INDEX idx_stock_movements_date ON stock_movements(created_at);
CREATE INDEX idx_purchase_orders_supplier ON purchase_orders(supplier_id);
CREATE INDEX idx_purchase_orders_date ON purchase_orders(order_date);

-- إنشاء عرض للمنتجات مع تفاصيل الفئة والمورد
CREATE VIEW products_view AS
SELECT 
    p.id,
    p.name,
    p.description,
    p.sku,
    p.unit_price,
    p.quantity_in_stock,
    p.minimum_stock_level,
    c.name as category_name,
    s.name as supplier_name,
    CASE 
        WHEN p.quantity_in_stock <= p.minimum_stock_level THEN 'منخفض'
        WHEN p.quantity_in_stock <= (p.minimum_stock_level * 2) THEN 'متوسط'
        ELSE 'جيد'
    END as stock_status,
    p.created_at,
    p.updated_at
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN suppliers s ON p.supplier_id = s.id;
